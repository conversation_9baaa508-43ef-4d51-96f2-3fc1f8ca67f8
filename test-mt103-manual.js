import { readFileSync } from 'fs';
import { processPaymentFile } from './server/utils/fileProcessors';

try {
  const content = readFileSync('./test-mt103-sample.txt', 'utf8');
  console.log('Testing MT103 parser with your sample file...\n');
  
  const result = processPaymentFile(content, 'MT103');
  
  console.log('Parsing Results:');
  console.log('================');
  console.log(`Reference: ${result.reference}`);
  console.log(`Amount: $${result.amount}`);
  console.log(`Sender: ${result.sender}`);
  console.log(`Recipient: ${result.recipient}`);
  console.log(`Recipient Address: ${result.recipient_address}`);
  console.log(`Due Date: ${result.due_date.toISOString().split('T')[0]}`);
  console.log(`File Type: ${result.file_type}`);
  
  console.log('\n✅ MT103 parser is working correctly!');
  
  // Verify the specific values from your sample
  if (result.amount === 32049.99) {
    console.log('✅ Amount correctly parsed: $32,049.99');
  } else {
    console.log(`❌ Amount parsing failed. Expected: 32049.99, Got: ${result.amount}`);
  }
  
  if (result.due_date.getFullYear() === 2025 && result.due_date.getMonth() === 3 && result.due_date.getDate() === 17) {
    console.log('✅ Due date correctly parsed: April 17, 2025');
  } else {
    console.log(`❌ Due date parsing failed. Got: ${result.due_date}`);
  }
  
  if (result.sender.includes('MediPharma GmbH')) {
    console.log('✅ Sender correctly parsed: MediPharma GmbH');
  } else {
    console.log(`❌ Sender parsing failed. Got: ${result.sender}`);
  }
  
  if (result.recipient.includes('NordicTrade AS')) {
    console.log('✅ Recipient correctly parsed: NordicTrade AS');
  } else {
    console.log(`❌ Recipient parsing failed. Got: ${result.recipient}`);
  }
  
} catch (error) {
  console.error('❌ Error testing MT103 parser:', error.message);
}
